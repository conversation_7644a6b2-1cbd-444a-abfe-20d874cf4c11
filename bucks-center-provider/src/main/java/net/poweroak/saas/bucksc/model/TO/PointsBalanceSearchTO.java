package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/11 13:53
 * @description
 */
@Data
public class PointsBalanceSearchTO extends PageInfo {
//    @ApiModelProperty(value = "uid")
//    private String uid;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "用户昵称")
    private String uname;
    @ApiModelProperty(value = "用户邮箱")
    private String email;

    // 新增筛选条件
    @ApiModelProperty(value = "账户注册时间-开始")
    private String creTimeStart;
    @ApiModelProperty(value = "账户注册时间-结束")
    private String creTimeEnd;

    @ApiModelProperty(value = "累计发放-最小值")
    private Integer earnMin;
    @ApiModelProperty(value = "累计发放-最大值")
    private Integer earnMax;

    @ApiModelProperty(value = "累计消耗-最小值")
    private Integer spendMin;
    @ApiModelProperty(value = "累计消耗-最大值")
    private Integer spendMax;

    @ApiModelProperty(value = "剩余积分-最小值")
    private Integer balanceMin;
    @ApiModelProperty(value = "剩余积分-最大值")
    private Integer balanceMax;

}
